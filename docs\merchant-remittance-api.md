# Merchant Remittance API Documentation

## Overview
Three new API endpoints have been created for the merchant remittance system that allow merchants to:
1. Get available send currencies
2. Get available receive currencies  
3. Calculate remittance fees

All endpoints require merchant authentication using the existing `auth:api` and `apiKYC` middleware.

## Authentication
All endpoints require:
- Valid API token in Authorization header: `Bearer {token}`
- User must be a merchant (`merchant = 1`)
- User must be active (`status = 1`)
- User must have completed KYC verification

## Endpoints

### 1. Get Available Send Currencies
**GET** `/api/merchant/remittance/send-currencies`

Returns all active currencies that can be used as source currencies for sending remittances.

**Response:**
```json
{
    "status": true,
    "message": "Send currencies retrieved successfully",
    "data": {
        "currencies": [
            {
                "id": 1,
                "name": "United States",
                "code": "USD",
                "minimum_amount": 10.0,
                "maximum_amount": 10000.0,
                "rate": 1.0,
                "flag": "https://example.com/flags/us.png"
            }
        ],
        "total_count": 1
    }
}
```

### 2. Get Available Receive Currencies
**GET** `/api/merchant/remittance/receive-currencies`

Returns all active currencies that can be received in remittance transactions.

**Optional Parameters:**
- `merchant_country_only=1` - Filter to only show merchant's assigned country

**Response:**
```json
{
    "status": true,
    "message": "Receive currencies retrieved successfully",
    "data": {
        "currencies": [
            {
                "id": 2,
                "name": "Nigeria",
                "code": "NGN",
                "minimum_amount": 1000.0,
                "maximum_amount": 5000000.0,
                "rate": 750.0,
                "flag": "https://example.com/flags/ng.png"
            }
        ],
        "total_count": 1,
        "merchant_country_id": null
    }
}
```

### 3. Calculate Remittance Fees
**POST** `/api/merchant/remittance/calculate-fees`

Calculates total fees for a remittance transaction including service charges, merchant commissions, and exchange rates.

**Request Body:**
```json
{
    "amount": 100,
    "send_currency_id": 1,
    "receive_currency_id": 2,
    "service_id": 1
}
```

**Validation Rules:**
- `amount`: required, numeric, minimum 0.01
- `send_currency_id`: required, must exist in countries table
- `receive_currency_id`: required, must exist in countries table  
- `service_id`: required, must exist in services table

**Response:**
```json
{
    "status": true,
    "message": "Fees calculated successfully",
    "data": {
        "send_amount": 100.0,
        "send_currency": "USD",
        "receive_currency": "NGN",
        "exchange_rate": 750.0,
        "service_fees": 5.0,
        "total_payable": 105.0,
        "recipient_gets": 75000.0,
        "merchant_commission": 0.25,
        "service_name": "Bank Transfer",
        "calculation_details": {
            "base_amount": 100.0,
            "service_charge": 5.0,
            "total_with_fees": 105.0,
            "total_payable_rounded": 105.0,
            "exchange_calculation": {
                "send_rate": 1.0,
                "receive_rate": 750.0,
                "calculated_rate": 750.0
            }
        }
    }
}
```

## Error Responses

### Authentication Errors
```json
{
    "status": false,
    "message": "Unauthorized merchant access"
}
```

### Validation Errors
```json
{
    "status": false,
    "message": "The amount field is required."
}
```

### Business Logic Errors
```json
{
    "status": false,
    "message": "Amount must be between 10 and 10000 USD"
}
```

## Testing Instructions

### Manual Testing with Postman/cURL

1. **Get API Token**: First authenticate and get an API token for a merchant user

2. **Test Send Currencies**:
```bash
curl -X GET "https://your-domain.com/api/merchant/remittance/send-currencies" \
  -H "Authorization: Bearer {your-token}" \
  -H "Accept: application/json"
```

3. **Test Receive Currencies**:
```bash
curl -X GET "https://your-domain.com/api/merchant/remittance/receive-currencies" \
  -H "Authorization: Bearer {your-token}" \
  -H "Accept: application/json"
```

4. **Test Fee Calculation**:
```bash
curl -X POST "https://your-domain.com/api/merchant/remittance/calculate-fees" \
  -H "Authorization: Bearer {your-token}" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100,
    "send_currency_id": 1,
    "receive_currency_id": 2,
    "service_id": 1
  }'
```

### Automated Testing

Run the test suite (requires composer dependencies):
```bash
php artisan test tests/Feature/MerchantRemittanceApiTest.php
```

## Implementation Notes

- All endpoints follow existing merchant API authentication patterns
- Fee calculations use the existing `getCharge()` helper function
- Amounts are rounded up using `ceil()` as per system requirements
- Merchant commissions are calculated using `config('basic.merchant_commission')`
- Exchange rates are calculated as: `receive_rate / send_rate`
- Proper input validation and error handling implemented
- Consistent JSON response format maintained

## Files Created/Modified

1. **Controller**: `app/Http/Controllers/API/MerchantRemittanceController.php`
2. **Routes**: Added routes to `routes/api.php`
3. **Tests**: `tests/Feature/MerchantRemittanceApiTest.php`
4. **Factories**: `database/factories/CountryFactory.php`, `database/factories/ServiceFactory.php`
5. **Documentation**: This file
